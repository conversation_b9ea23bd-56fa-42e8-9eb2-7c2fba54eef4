# ATMA Backend Load Test Strategies

## Overview
Load test script telah dimodifikasi untuk mendukung berbagai strategi pengujian beban dengan konfigurasi batch size yang fleksibel.

## Cara Penggunaan

### 1. Menggunakan Batch Script (Recommended)
```bash
run-load-tests.bat
```
Script ini menyediakan menu interaktif untuk memilih strategi load test.

### 2. Manual Command Line
```bash
node load-test-e2e.js [users] [delay_ms] [batch_size]
```

## Strategi Load Testing

### 1. Conservative (Default) - 20 batches of 5 users
```bash
node load-test-e2e.js 100 1000 5
```
- **Tujuan**: Simulasi beban bertahap yang aman
- **Karakteristik**: 
  - 5 user concurrent per batch
  - 1 detik delay antar batch
  - Total 20 batch untuk 100 user
- **Cocok untuk**: Testing awal, sistem dengan resource terbatas

### 2. Moderate - 10 batches of 10 users
```bash
node load-test-e2e.js 100 1000 10
```
- **Tujuan**: Simulasi beban menengah
- **Karakteristik**:
  - 10 user concurrent per batch
  - 1 detik delay antar batch
  - Total 10 batch untuk 100 user
- **Cocok untuk**: Testing performa normal, validasi kapasitas standar

### 3. Aggressive - 5 batches of 20 users
```bash
node load-test-e2e.js 100 1000 20
```
- **Tujuan**: Simulasi beban tinggi per batch
- **Karakteristik**:
  - 20 user concurrent per batch
  - 1 detik delay antar batch
  - Total 5 batch untuk 100 user
- **Cocok untuk**: Testing kapasitas maksimal, stress testing

### 4. Maximum Load - 100 users all at once
```bash
node load-test-e2e.js 100 500 100
```
- **Tujuan**: Simulasi beban puncak ekstrem
- **Karakteristik**:
  - 100 user concurrent sekaligus
  - Tidak ada batching
  - Delay minimal (500ms)
- **Cocok untuk**: Stress testing, testing breaking point
- **⚠️ WARNING**: Dapat membuat sistem overload

### 5. Quick Test - 5 users
```bash
node load-test-e2e.js 5 0 5
```
- **Tujuan**: Testing cepat untuk validasi fungsionalitas
- **Karakteristik**:
  - 5 user concurrent
  - Tidak ada delay
  - Single batch
- **Cocok untuk**: Development testing, quick validation

## Interpretasi Hasil

### Success Rate
- **95%+**: Excellent - Sistem menangani beban dengan sangat baik
- **80-94%**: Good - Sistem menangani sebagian besar request, perlu optimisasi minor
- **60-79%**: Fair - Sistem kesulitan, perlu optimisasi signifikan
- **<60%**: Poor - Sistem gagal menangani beban, perlu perbaikan major

### Timing Metrics
- **Registration**: Waktu untuk registrasi user baru
- **Login**: Waktu untuk autentikasi
- **Submission**: Waktu untuk submit assessment
- **Processing**: Waktu untuk AI processing (paling kritis)
- **End-to-End**: Total waktu dari registrasi hingga hasil

### Error Analysis
- **Timeout errors**: Indikasi kapasitas AI processing tidak cukup
- **Submission errors**: Indikasi bottleneck di assessment service
- **Login/Registration errors**: Indikasi masalah database atau auth service

## Rekomendasi Penggunaan

### Development Phase
1. Mulai dengan **Quick Test** (5 users)
2. Lanjut ke **Conservative** (20 batches of 5)
3. Jika berhasil, coba **Moderate** (10 batches of 10)

### Pre-Production Testing
1. **Conservative** untuk baseline
2. **Moderate** untuk validasi kapasitas normal
3. **Aggressive** untuk testing batas atas
4. **Maximum Load** untuk stress testing (hati-hati!)

### Production Monitoring
- Gunakan **Conservative** untuk monitoring rutin
- **Moderate** untuk validasi setelah deployment
- **Aggressive** hanya jika diperlukan untuk testing kapasitas puncak

## Tips Optimisasi

### Jika Success Rate Rendah:
1. Periksa log error untuk pattern
2. Monitor resource usage (CPU, Memory, Database)
3. Pertimbangkan scaling horizontal
4. Optimisasi query database
5. Tune AI processing pipeline

### Jika Processing Time Tinggi:
1. Periksa AI service capacity
2. Implementasi caching jika memungkinkan
3. Optimisasi AI model inference
4. Pertimbangkan queue management

### Jika Memory/CPU Usage Tinggi:
1. Profile aplikasi untuk memory leaks
2. Optimisasi algoritma yang resource-intensive
3. Implementasi connection pooling
4. Tune garbage collection settings
