# ATMA Backend Load Test Strategies

## Overview
Load test script telah dimodifikasi untuk mendukung berbagai strategi pengujian beban dengan konfigurasi batch size yang fleksibel.

## Cara Penggunaan

### 1. Menggunakan Batch Script (Recommended)
```bash
run-load-tests.bat
```
Script ini menyediakan menu interaktif untuk memilih strategi load test.

### 2. Manual Command Line
```bash
node load-test-e2e.js [users] [delay_ms] [batch_size]
```

## Strategi Load Testing

### 1. Conservative (Default) - 20 batches of 5 users
```bash
node load-test-e2e.js 100 1000 5
```
- **Tujuan**: Simulasi beban bertahap yang aman
- **Karakteristik**: 
  - 5 user concurrent per batch
  - 1 detik delay antar batch
  - Total 20 batch untuk 100 user
- **Cocok untuk**: Testing awal, sistem dengan resource terbatas

### 2. Moderate - 10 batches of 10 users
```bash
node load-test-e2e.js 100 1000 10
```
- **Tujuan**: Simulasi beban menengah
- **Karakteristik**:
  - 10 user concurrent per batch
  - 1 detik delay antar batch
  - Total 10 batch untuk 100 user
- **Cocok untuk**: Testing performa normal, validasi kapasitas standar

### 3. Aggressive - 5 batches of 20 users
```bash
node load-test-e2e.js 100 1000 20
```
- **Tujuan**: Simulasi beban tinggi per batch
- **Karakteristik**:
  - 20 user concurrent per batch
  - 1 detik delay antar batch
  - Total 5 batch untuk 100 user
- **Cocok untuk**: Testing kapasitas maksimal, stress testing

### 4. Maximum Load - 100 users all at once
```bash
node load-test-e2e.js 100 500 100
```
- **Tujuan**: Simulasi beban puncak ekstrem
- **Karakteristik**:
  - 100 user concurrent sekaligus
  - Tidak ada batching
  - Delay minimal (500ms)
- **Cocok untuk**: Stress testing, testing breaking point
- **⚠️ WARNING**: Dapat membuat sistem overload

### 5. Quick Test - 5 users
```bash
node load-test-e2e.js 5 0 5
```
- **Tujuan**: Testing cepat untuk validasi fungsionalitas
- **Karakteristik**:
  - 5 user concurrent
  - Tidak ada delay
  - Single batch
- **Cocok untuk**: Development testing, quick validation

## Interpretasi Hasil

### Overall Success Rate
- **95%+**: Excellent - Sistem menangani beban dengan sangat baik
- **80-94%**: Good - Sistem menangani sebagian besar request, perlu optimisasi minor
- **60-79%**: Fair - Sistem kesulitan, perlu optimisasi signifikan
- **<60%**: Poor - Sistem gagal menangani beban, perlu perbaikan major

### Step-by-Step Success Breakdown
Load test sekarang menampilkan success rate untuk setiap tahap:
- **👤 Registration**: Berapa banyak user yang berhasil mendaftar
- **🔐 Login**: Berapa banyak user yang berhasil login
- **📊 Assessment Submission**: Berapa banyak user yang berhasil submit assessment
- **🤖 AI Processing**: Berapa banyak user yang berhasil menyelesaikan AI processing

### Conversion Funnel Analysis
Menampilkan conversion rate antar tahap untuk mengidentifikasi di mana user "drop off":
```
5 Users Started
↓ 100.0% conversion
5 Registered Successfully
↓ 100.0% conversion
5 Logged In Successfully
↓ 100.0% conversion
5 Submitted Assessment Successfully
↓ 100.0% conversion
5 Completed Full Journey
```

### Summary Section
Memberikan ringkasan yang jelas:
- Jumlah user yang berhasil di setiap tahap
- Jumlah user yang gagal menyelesaikan journey

### Timing Metrics
- **Registration**: Waktu untuk registrasi user baru
- **Login**: Waktu untuk autentikasi
- **Submission**: Waktu untuk submit assessment
- **Processing**: Waktu untuk AI processing (paling kritis)
- **End-to-End**: Total waktu dari registrasi hingga hasil

### Bottleneck Analysis
Sistem sekarang secara otomatis mengidentifikasi bottleneck:
- **🚨 Registration bottleneck**: Masalah di tahap registrasi
- **🚨 Login bottleneck**: Masalah di tahap autentikasi
- **🚨 Assessment submission bottleneck**: Masalah di tahap submit
- **🚨 AI processing bottleneck**: Masalah di tahap AI processing
- **🚨 Timeout bottleneck**: Masalah timeout

### Error Analysis
- **Registration Errors**: Indikasi masalah database atau validation
- **Login Errors**: Indikasi masalah auth service atau database
- **Submission Errors**: Indikasi bottleneck di assessment service
- **Processing Errors**: Indikasi masalah AI service atau worker
- **Timeout Errors**: Indikasi kapasitas AI processing tidak cukup

## Rekomendasi Penggunaan

### Development Phase
1. Mulai dengan **Quick Test** (5 users)
2. Lanjut ke **Conservative** (20 batches of 5)
3. Jika berhasil, coba **Moderate** (10 batches of 10)

### Pre-Production Testing
1. **Conservative** untuk baseline
2. **Moderate** untuk validasi kapasitas normal
3. **Aggressive** untuk testing batas atas
4. **Maximum Load** untuk stress testing (hati-hati!)

### Production Monitoring
- Gunakan **Conservative** untuk monitoring rutin
- **Moderate** untuk validasi setelah deployment
- **Aggressive** hanya jika diperlukan untuk testing kapasitas puncak

## Tips Optimisasi

### Jika Success Rate Rendah:
1. Periksa log error untuk pattern
2. Monitor resource usage (CPU, Memory, Database)
3. Pertimbangkan scaling horizontal
4. Optimisasi query database
5. Tune AI processing pipeline

### Jika Processing Time Tinggi:
1. Periksa AI service capacity
2. Implementasi caching jika memungkinkan
3. Optimisasi AI model inference
4. Pertimbangkan queue management

### Jika Memory/CPU Usage Tinggi:
1. Profile aplikasi untuk memory leaks
2. Optimisasi algoritma yang resource-intensive
3. Implementasi connection pooling
4. Tune garbage collection settings

## Fitur Summary Baru

### Detailed Step-by-Step Breakdown
Load test sekarang menampilkan breakdown detail untuk setiap tahap:

```
📈 STEP-BY-STEP SUCCESS BREAKDOWN:
   👤 Registration: 20/20 (100.0%)
   🔐 Login: 20/20 (100.0%)
   📊 Assessment Submission: 20/20 (100.0%)
   🤖 AI Processing: 20/20 (100.0%)
```

### Conversion Funnel Analysis
Menampilkan conversion rate antar tahap:

```
🔄 CONVERSION FUNNEL ANALYSIS:
   20 Users Started
   ↓ 100.0% conversion
   20 Registered Successfully
   ↓ 100.0% conversion
   20 Logged In Successfully
   ↓ 100.0% conversion
   20 Submitted Assessment Successfully
   ↓ 100.0% conversion
   20 Completed Full Journey
```

### Summary Section
Ringkasan yang jelas dan mudah dibaca:

```
📋 SUMMARY:
   • 20 users successfully registered
   • 20 users successfully logged in
   • 20 users successfully submitted assessments
   • 20 users successfully completed AI processing
   • 0 users failed to complete the full journey
```

### Automatic Bottleneck Detection
Sistem secara otomatis mengidentifikasi bottleneck:

```
🔍 BOTTLENECK ANALYSIS:
   ✅ No bottlenecks detected - all operations completed successfully

   // Atau jika ada masalah:
   🚨 Registration bottleneck: 5 failures (5.0%)
   🚨 AI processing bottleneck: 10 timeouts (10.0%)
```

### Smart Recommendations
Rekomendasi yang disesuaikan dengan hasil test:

```
💡 RECOMMENDATIONS:
   - Registration success rate is low, check database capacity
   - AI processing success rate is low, check worker capacity and AI service
   - End-to-end time is high, consider optimizing the processing pipeline
```

## Menggunakan Summary untuk Debugging

### 1. Conversion Funnel Analysis
Identifikasi di tahap mana user paling banyak gagal:
- Jika drop off di Registration → masalah database/validation
- Jika drop off di Login → masalah auth service
- Jika drop off di Submission → masalah assessment service
- Jika drop off di Processing → masalah AI service/worker

### 2. Step-by-Step Breakdown
Lihat persentase keberhasilan setiap tahap:
- <95% Registration → check database capacity
- <95% Login → check auth service performance
- <95% Submission → check assessment service
- <95% Processing → check AI worker capacity

### 3. Bottleneck Analysis
Fokus pada tahap dengan error tertinggi:
- Registration bottleneck → database tuning
- Login bottleneck → auth service scaling
- Submission bottleneck → rate limiting review
- Processing bottleneck → worker scaling

### 4. Timing Statistics
Identifikasi tahap yang paling lambat:
- High Registration time → database optimization
- High Login time → auth service optimization
- High Submission time → API optimization
- High Processing time → AI model optimization
