@echo off
echo ========================================
echo ATMA Backend Load Test Runner
echo ========================================
echo.
echo Available test scenarios:
echo.
echo 1. Conservative (20 batches of 5 users) - Default
echo 2. Moderate (10 batches of 10 users)
echo 3. Aggressive (5 batches of 20 users)
echo 4. Maximum Load (100 users all at once)
echo 5. Custom configuration
echo 6. Quick test (5 users, 1 batch)
echo.

set /p choice="Select test scenario (1-6): "

if "%choice%"=="1" (
    echo.
    echo Running CONSERVATIVE test: 100 users in 20 batches of 5
    echo This simulates gradual load increase...
    node load-test-e2e.js 100 1000 5
) else if "%choice%"=="2" (
    echo.
    echo Running MODERATE test: 100 users in 10 batches of 10
    echo This simulates moderate concurrent load...
    node load-test-e2e.js 100 1000 10
) else if "%choice%"=="3" (
    echo.
    echo Running AGGRESSIVE test: 100 users in 5 batches of 20
    echo This simulates high concurrent load per batch...
    node load-test-e2e.js 100 1000 20
) else if "%choice%"=="4" (
    echo.
    echo Running MAXIMUM LOAD test: 100 users ALL AT ONCE
    echo This simulates peak load scenario...
    echo WARNING: This may overwhelm the system!
    echo.
    set /p confirm="Are you sure? (y/N): "
    if /i "!confirm!"=="y" (
        node load-test-e2e.js 100 500 100
    ) else (
        echo Test cancelled.
        goto end
    )
) else if "%choice%"=="5" (
    echo.
    echo Custom configuration:
    set /p users="Number of users (default 100): "
    set /p delay="Delay between batches in ms (default 1000): "
    set /p batch="Batch size (default 5): "
    
    if "%users%"=="" set users=100
    if "%delay%"=="" set delay=1000
    if "%batch%"=="" set batch=5
    
    echo.
    echo Running CUSTOM test: %users% users, batch size %batch%, delay %delay%ms
    node load-test-e2e.js %users% %delay% %batch%
) else if "%choice%"=="6" (
    echo.
    echo Running QUICK test: 5 users in 1 batch
    echo This is for quick functionality testing...
    node load-test-e2e.js 5 0 5
) else (
    echo Invalid choice. Please select 1-6.
    goto end
)

:end
echo.
echo Press any key to exit...
pause >nul
