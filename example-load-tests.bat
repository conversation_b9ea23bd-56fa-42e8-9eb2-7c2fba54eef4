@echo off
echo ========================================
echo ATMA Load Test Examples
echo ========================================
echo.
echo This script demonstrates different load testing scenarios
echo with the new detailed summary reporting.
echo.

:menu
echo Available test examples:
echo.
echo 1. Quick Test (5 users) - See detailed summary format
echo 2. Conservative Test (100 users, 20 batches of 5)
echo 3. Moderate Test (100 users, 10 batches of 10)
echo 4. Aggressive Test (100 users, 5 batches of 20)
echo 5. Stress Test (50 users all at once)
echo 6. Custom Test
echo 7. Exit
echo.

set /p choice="Select test example (1-7): "

if "%choice%"=="1" (
    echo.
    echo ========================================
    echo QUICK TEST - 5 Users
    echo ========================================
    echo This test shows the new detailed summary format
    echo with step-by-step success breakdown and conversion funnel.
    echo.
    pause
    node load-test-e2e.js 5 0 5
    goto :show_summary_explanation
) else if "%choice%"=="2" (
    echo.
    echo ========================================
    echo CONSERVATIVE TEST - 100 Users
    echo ========================================
    echo 20 batches of 5 users each with 1 second delay
    echo This is the safest approach for load testing.
    echo.
    pause
    node load-test-e2e.js 100 1000 5
    goto :end
) else if "%choice%"=="3" (
    echo.
    echo ========================================
    echo MODERATE TEST - 100 Users
    echo ========================================
    echo 10 batches of 10 users each with 1 second delay
    echo This tests normal concurrent load capacity.
    echo.
    pause
    node load-test-e2e.js 100 1000 10
    goto :end
) else if "%choice%"=="4" (
    echo.
    echo ========================================
    echo AGGRESSIVE TEST - 100 Users
    echo ========================================
    echo 5 batches of 20 users each with 1 second delay
    echo This tests high concurrent load per batch.
    echo.
    pause
    node load-test-e2e.js 100 1000 20
    goto :end
) else if "%choice%"=="5" (
    echo.
    echo ========================================
    echo STRESS TEST - 50 Users All At Once
    echo ========================================
    echo WARNING: This will hit the system with maximum load!
    echo All 50 users will start simultaneously.
    echo.
    set /p confirm="Are you sure you want to proceed? (y/N): "
    if /i "!confirm!"=="y" (
        node load-test-e2e.js 50 500 50
    ) else (
        echo Test cancelled.
    )
    goto :end
) else if "%choice%"=="6" (
    echo.
    echo ========================================
    echo CUSTOM TEST
    echo ========================================
    set /p users="Number of users: "
    set /p delay="Delay between batches (ms): "
    set /p batch="Batch size: "
    echo.
    echo Running custom test: %users% users, batch size %batch%, delay %delay%ms
    node load-test-e2e.js %users% %delay% %batch%
    goto :end
) else if "%choice%"=="7" (
    goto :end
) else (
    echo Invalid choice. Please select 1-7.
    echo.
    goto :menu
)

:show_summary_explanation
echo.
echo ========================================
echo SUMMARY EXPLANATION
echo ========================================
echo.
echo The new load test summary includes:
echo.
echo 📈 STEP-BY-STEP SUCCESS BREAKDOWN:
echo    Shows success count and percentage for each stage:
echo    - Registration, Login, Submission, AI Processing
echo.
echo 🔄 CONVERSION FUNNEL ANALYSIS:
echo    Shows conversion rates between stages to identify
echo    where users "drop off" in the process
echo.
echo 📋 SUMMARY:
echo    Clear count of successful users at each stage
echo    and how many failed to complete the journey
echo.
echo 🔍 BOTTLENECK ANALYSIS:
echo    Automatically identifies which stage has issues
echo    and provides specific recommendations
echo.
echo 💡 RECOMMENDATIONS:
echo    Targeted suggestions based on the test results
echo    and identified bottlenecks
echo.

:end
echo.
echo Press any key to exit...
pause >nul
